<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas虚拟列表 - 高级演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .controls {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .control-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .control-group:last-child {
            margin-bottom: 0;
        }
        
        .control-group label {
            font-weight: 600;
            min-width: 100px;
        }
        
        .input-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        input, select, button {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.2s;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        button {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            cursor: pointer;
            font-weight: 500;
        }
        
        button:hover {
            background: #2563eb;
            border-color: #2563eb;
        }
        
        button:disabled {
            background: #9ca3af;
            border-color: #9ca3af;
            cursor: not-allowed;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 2rem;
        }
        
        .list-section {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .list-header {
            background: #f8fafc;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-container {
            height: 600px;
            position: relative;
        }
        
        #listCanvas {
            display: block;
            cursor: pointer;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .stats-panel, .selection-panel {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .panel-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #374151;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .stat-label {
            color: #6b7280;
        }
        
        .stat-value {
            font-weight: 600;
            color: #1f2937;
        }
        
        .selection-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 0.5rem;
        }
        
        .selection-item {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            background: #f3f4f6;
            margin-bottom: 0.25rem;
            border-radius: 4px;
        }
        
        .search-box {
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .performance-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            color: #6b7280;
        }
        
        .fps-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }
        
        .fps-indicator.warning {
            background: #f59e0b;
        }
        
        .fps-indicator.error {
            background: #ef4444;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .control-group {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .control-group label {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Canvas虚拟列表</h1>
        <p>高性能长列表渲染解决方案 - 支持百万级数据流畅滚动</p>
    </div>
    
    <div class="container">
        <div class="controls">
            <div class="control-group">
                <label>数据量:</label>
                <div class="input-group">
                    <input type="number" id="dataCount" value="100000" min="1000" max="10000000" step="1000">
                    <select id="dataType">
                        <option value="simple">简单数据</option>
                        <option value="complex">复杂数据</option>
                        <option value="mixed">混合数据</option>
                    </select>
                    <button onclick="generateData()">生成数据</button>
                </div>
            </div>
            
            <div class="control-group">
                <label>操作:</label>
                <div class="input-group">
                    <button onclick="scrollToTop()">回到顶部</button>
                    <button onclick="scrollToBottom()">滚动到底部</button>
                    <button onclick="scrollToRandom()">随机位置</button>
                    <button onclick="selectAll()">全选</button>
                    <button onclick="clearSelection()">清除选择</button>
                </div>
            </div>
            
            <div class="control-group">
                <label>搜索:</label>
                <div class="input-group">
                    <input type="text" id="searchInput" placeholder="输入搜索关键词..." class="search-box">
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="list-section">
                <div class="list-header">
                    <h3>虚拟列表</h3>
                    <div class="performance-indicator">
                        <div class="fps-indicator" id="fpsIndicator"></div>
                        <span id="fpsText">60 FPS</span>
                    </div>
                </div>
                <div class="list-container">
                    <canvas id="listCanvas"></canvas>
                </div>
            </div>
            
            <div class="sidebar">
                <div class="stats-panel">
                    <div class="panel-title">性能统计</div>
                    <div class="stat-item">
                        <span class="stat-label">总数据量:</span>
                        <span class="stat-value" id="totalCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">可见范围:</span>
                        <span class="stat-value" id="visibleRange">0-0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">渲染耗时:</span>
                        <span class="stat-value" id="renderTime">0ms</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">帧率:</span>
                        <span class="stat-value" id="fps">60 FPS</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">内存使用:</span>
                        <span class="stat-value" id="memoryUsage">0MB</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">滚动位置:</span>
                        <span class="stat-value" id="scrollPosition">0%</span>
                    </div>
                </div>
                
                <div class="selection-panel">
                    <div class="panel-title">选中项目 (<span id="selectionCount">0</span>)</div>
                    <div class="selection-list" id="selectionList">
                        <div style="color: #9ca3af; text-align: center; padding: 1rem;">
                            暂无选中项目
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="advanced-canvas-list.js"></script>
    <script>
        let virtualList;
        let statsUpdateInterval;
        
        function initVirtualList() {
            const canvas = document.getElementById('listCanvas');
            
            virtualList = new AdvancedCanvasVirtualList(canvas, {
                itemHeight: 60,
                padding: 16,
                fontSize: 14,
                enableSelection: true,
                enableHover: true,
                enableSearch: true,
                onItemClick: (index, item, event) => {
                    console.log('点击项目:', index, item);
                },
                onItemDoubleClick: (index, item, event) => {
                    console.log('双击项目:', index, item);
                    alert(`双击了项目: ${item.title || item}`);
                },
                onSelectionChange: (selectedIndices) => {
                    updateSelectionPanel(selectedIndices);
                },
                onScroll: (scrollTop, maxScroll) => {
                    updateScrollPosition(scrollTop, maxScroll);
                }
            });
            
            // 生成初始数据
            generateData();
            
            // 启动性能监控
            startStatsUpdate();
            
            // 绑定搜索事件
            document.getElementById('searchInput').addEventListener('input', (e) => {
                virtualList.search(e.target.value);
                updateStats();
            });
        }
        
        function generateData() {
            const count = parseInt(document.getElementById('dataCount').value);
            const type = document.getElementById('dataType').value;
            const data = [];
            
            for (let i = 0; i < count; i++) {
                let item;
                
                switch (type) {
                    case 'simple':
                        item = `列表项 ${i + 1}`;
                        break;
                    case 'complex':
                        item = {
                            id: i,
                            title: `复杂项目 ${i + 1}`,
                            subtitle: `描述信息 - 创建于 ${new Date(Date.now() - Math.random() * 86400000 * 365).toLocaleDateString()}`,
                            rightText: `${Math.floor(Math.random() * 1000)}`,
                            category: ['工作', '生活', '学习', '娱乐'][Math.floor(Math.random() * 4)],
                            priority: Math.floor(Math.random() * 5) + 1,
                            status: ['待处理', '进行中', '已完成'][Math.floor(Math.random() * 3)]
                        };
                        break;
                    case 'mixed':
                        if (i % 3 === 0) {
                            item = `简单项目 ${i + 1}`;
                        } else {
                            item = {
                                id: i,
                                title: `混合项目 ${i + 1}`,
                                subtitle: `随机数据: ${Math.random().toString(36).substring(7)}`,
                                rightText: new Date().toLocaleTimeString()
                            };
                        }
                        break;
                }
                
                data.push(item);
            }
            
            virtualList.setData(data);
            updateStats();
        }
        
        function startStatsUpdate() {
            if (statsUpdateInterval) {
                clearInterval(statsUpdateInterval);
            }
            
            statsUpdateInterval = setInterval(updateStats, 100);
        }
        
        function updateStats() {
            const stats = virtualList.getPerformanceStats();
            
            document.getElementById('totalCount').textContent = stats.totalItems.toLocaleString();
            document.getElementById('visibleRange').textContent = 
                `${virtualList.state.visibleStart}-${virtualList.state.visibleEnd}`;
            document.getElementById('renderTime').textContent = `${stats.renderTime.toFixed(2)}ms`;
            document.getElementById('fps').textContent = `${stats.fps} FPS`;
            document.getElementById('memoryUsage').textContent = `${stats.memoryUsage.toFixed(2)}MB`;
            
            // 更新FPS指示器
            const fpsIndicator = document.getElementById('fpsIndicator');
            const fpsText = document.getElementById('fpsText');
            
            if (stats.fps >= 50) {
                fpsIndicator.className = 'fps-indicator';
            } else if (stats.fps >= 30) {
                fpsIndicator.className = 'fps-indicator warning';
            } else {
                fpsIndicator.className = 'fps-indicator error';
            }
            
            fpsText.textContent = `${stats.fps} FPS`;
        }
        
        function updateSelectionPanel(selectedIndices) {
            const selectionCount = document.getElementById('selectionCount');
            const selectionList = document.getElementById('selectionList');
            
            selectionCount.textContent = selectedIndices.length;
            
            if (selectedIndices.length === 0) {
                selectionList.innerHTML = '<div style="color: #9ca3af; text-align: center; padding: 1rem;">暂无选中项目</div>';
            } else {
                const selectedItems = virtualList.getSelectedItems();
                selectionList.innerHTML = selectedItems.map((item, index) => {
                    const displayText = typeof item === 'object' ? 
                        (item.title || item.name || `项目 ${selectedIndices[index] + 1}`) : 
                        String(item);
                    return `<div class="selection-item">${displayText}</div>`;
                }).join('');
            }
        }
        
        function updateScrollPosition(scrollTop, maxScroll) {
            const percentage = maxScroll > 0 ? (scrollTop / maxScroll * 100).toFixed(1) : 0;
            document.getElementById('scrollPosition').textContent = `${percentage}%`;
        }
        
        // 公共操作函数
        function scrollToTop() {
            virtualList.scrollTo(0);
        }
        
        function scrollToBottom() {
            virtualList.scrollTo(virtualList.state.totalHeight);
        }
        
        function scrollToRandom() {
            const randomIndex = Math.floor(Math.random() * virtualList.state.filteredData.length);
            virtualList.scrollToIndex(randomIndex);
        }
        
        function selectAll() {
            virtualList.selectAll();
        }
        
        function clearSelection() {
            virtualList.clearSelection();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initVirtualList);
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (statsUpdateInterval) {
                clearInterval(statsUpdateInterval);
            }
            if (virtualList) {
                virtualList.destroy();
            }
        });
    </script>
</body>
</html>
