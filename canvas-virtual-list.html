<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas虚拟列表 - 高性能长列表渲染</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            padding: 20px;
            background: #2563eb;
            color: white;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .list-container {
            position: relative;
            height: 500px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        #listCanvas {
            display: block;
            cursor: pointer;
        }
        
        .scrollbar {
            position: absolute;
            right: 0;
            top: 0;
            width: 12px;
            height: 100%;
            background: #f3f4f6;
            border-left: 1px solid #e5e7eb;
        }
        
        .scrollbar-thumb {
            position: absolute;
            width: 100%;
            background: #9ca3af;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .scrollbar-thumb:hover {
            background: #6b7280;
        }
        
        .stats {
            padding: 20px;
            background: #f9fafb;
            font-size: 14px;
            color: #6b7280;
        }
        
        button {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        button:hover {
            background: #f3f4f6;
        }
        
        input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            width: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Canvas虚拟列表演示</h1>
            <p>高性能长列表渲染方案 - 支持百万级数据</p>
        </div>
        
        <div class="controls">
            <label>数据量：</label>
            <input type="number" id="dataCount" value="100000" min="1000" max="10000000">
            <button onclick="generateData()">生成数据</button>
            <button onclick="scrollToTop()">回到顶部</button>
            <button onclick="scrollToBottom()">滚动到底部</button>
        </div>
        
        <div class="list-container">
            <canvas id="listCanvas"></canvas>
            <div class="scrollbar">
                <div class="scrollbar-thumb"></div>
            </div>
        </div>
        
        <div class="stats">
            <div>总数据量: <span id="totalCount">0</span></div>
            <div>可见范围: <span id="visibleRange">0-0</span></div>
            <div>渲染耗时: <span id="renderTime">0ms</span></div>
            <div>内存使用: <span id="memoryUsage">0MB</span></div>
        </div>
    </div>

    <script>
        class CanvasVirtualList {
            constructor(canvas, options = {}) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.container = canvas.parentElement;
                
                // 配置参数
                this.itemHeight = options.itemHeight || 50;
                this.padding = options.padding || 10;
                this.fontSize = options.fontSize || 14;
                this.lineHeight = options.lineHeight || 20;
                
                // 数据和状态
                this.data = [];
                this.scrollTop = 0;
                this.containerHeight = 0;
                this.totalHeight = 0;
                this.visibleStart = 0;
                this.visibleEnd = 0;
                this.bufferSize = 5; // 缓冲区大小
                
                // 性能监控
                this.renderTime = 0;
                this.lastRenderTime = 0;
                
                this.init();
            }
            
            init() {
                this.setupCanvas();
                this.bindEvents();
                this.setupScrollbar();
            }
            
            setupCanvas() {
                const rect = this.container.getBoundingClientRect();
                const dpr = window.devicePixelRatio || 1;
                
                this.containerHeight = rect.height;
                this.canvas.width = (rect.width - 12) * dpr; // 减去滚动条宽度
                this.canvas.height = rect.height * dpr;
                
                this.canvas.style.width = (rect.width - 12) + 'px';
                this.canvas.style.height = rect.height + 'px';
                
                this.ctx.scale(dpr, dpr);
                this.ctx.font = `${this.fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
            }
            
            bindEvents() {
                // 鼠标滚轮事件
                this.container.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    this.handleScroll(e.deltaY);
                });
                
                // 键盘事件
                this.canvas.addEventListener('keydown', (e) => {
                    switch(e.key) {
                        case 'ArrowUp':
                            e.preventDefault();
                            this.handleScroll(-this.itemHeight);
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            this.handleScroll(this.itemHeight);
                            break;
                        case 'PageUp':
                            e.preventDefault();
                            this.handleScroll(-this.containerHeight);
                            break;
                        case 'PageDown':
                            e.preventDefault();
                            this.handleScroll(this.containerHeight);
                            break;
                        case 'Home':
                            e.preventDefault();
                            this.scrollTo(0);
                            break;
                        case 'End':
                            e.preventDefault();
                            this.scrollTo(this.totalHeight);
                            break;
                    }
                });
                
                // 点击事件
                this.canvas.addEventListener('click', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    const y = e.clientY - rect.top;
                    const index = Math.floor((this.scrollTop + y) / this.itemHeight);
                    
                    if (index >= 0 && index < this.data.length) {
                        this.onItemClick(index, this.data[index]);
                    }
                });
                
                // 使canvas可聚焦
                this.canvas.tabIndex = 0;
                
                // 窗口大小变化
                window.addEventListener('resize', () => {
                    this.setupCanvas();
                    this.render();
                });
            }

            setupScrollbar() {
                this.scrollbar = this.container.querySelector('.scrollbar');
                this.scrollbarThumb = this.container.querySelector('.scrollbar-thumb');

                // 滚动条拖拽
                let isDragging = false;
                let startY = 0;
                let startScrollTop = 0;

                this.scrollbarThumb.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    startY = e.clientY;
                    startScrollTop = this.scrollTop;
                    document.addEventListener('mousemove', onMouseMove);
                    document.addEventListener('mouseup', onMouseUp);
                });

                const onMouseMove = (e) => {
                    if (!isDragging) return;

                    const deltaY = e.clientY - startY;
                    const scrollbarHeight = this.scrollbar.offsetHeight;
                    const thumbHeight = this.scrollbarThumb.offsetHeight;
                    const maxScroll = this.totalHeight - this.containerHeight;
                    const scrollRatio = deltaY / (scrollbarHeight - thumbHeight);

                    this.scrollTo(startScrollTop + scrollRatio * maxScroll);
                };

                const onMouseUp = () => {
                    isDragging = false;
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                };
            }

            setData(data) {
                this.data = data;
                this.totalHeight = data.length * this.itemHeight;
                this.updateScrollbar();
                this.calculateVisibleRange();
                this.render();
                this.updateStats();
            }

            handleScroll(deltaY) {
                const newScrollTop = Math.max(0, Math.min(
                    this.scrollTop + deltaY,
                    this.totalHeight - this.containerHeight
                ));

                if (newScrollTop !== this.scrollTop) {
                    this.scrollTo(newScrollTop);
                }
            }

            scrollTo(scrollTop) {
                this.scrollTop = Math.max(0, Math.min(
                    scrollTop,
                    this.totalHeight - this.containerHeight
                ));

                this.calculateVisibleRange();
                this.updateScrollbar();
                this.render();
                this.updateStats();
            }

            calculateVisibleRange() {
                const start = Math.floor(this.scrollTop / this.itemHeight);
                const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);

                this.visibleStart = Math.max(0, start - this.bufferSize);
                this.visibleEnd = Math.min(
                    this.data.length - 1,
                    start + visibleCount + this.bufferSize
                );
            }

            updateScrollbar() {
                if (this.totalHeight <= this.containerHeight) {
                    this.scrollbar.style.display = 'none';
                    return;
                }

                this.scrollbar.style.display = 'block';

                const scrollbarHeight = this.scrollbar.offsetHeight;
                const thumbHeight = Math.max(20,
                    (this.containerHeight / this.totalHeight) * scrollbarHeight
                );
                const thumbTop = (this.scrollTop / (this.totalHeight - this.containerHeight)) *
                    (scrollbarHeight - thumbHeight);

                this.scrollbarThumb.style.height = thumbHeight + 'px';
                this.scrollbarThumb.style.top = thumbTop + 'px';
            }

            render() {
                const startTime = performance.now();

                // 清空画布
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // 渲染可见项
                for (let i = this.visibleStart; i <= this.visibleEnd; i++) {
                    if (i >= this.data.length) break;

                    const item = this.data[i];
                    const y = i * this.itemHeight - this.scrollTop;

                    // 跳过不在可视区域的项
                    if (y + this.itemHeight < 0 || y > this.containerHeight) continue;

                    this.renderItem(item, i, y);
                }

                this.renderTime = performance.now() - startTime;
                this.lastRenderTime = Date.now();
            }

            renderItem(item, index, y) {
                const isEven = index % 2 === 0;
                const isHovered = this.hoveredIndex === index;

                // 背景
                this.ctx.fillStyle = isHovered ? '#e0f2fe' : (isEven ? '#ffffff' : '#f8fafc');
                this.ctx.fillRect(0, y, this.canvas.width, this.itemHeight);

                // 分割线
                this.ctx.strokeStyle = '#e2e8f0';
                this.ctx.lineWidth = 1;
                this.ctx.beginPath();
                this.ctx.moveTo(0, y + this.itemHeight);
                this.ctx.lineTo(this.canvas.width, y + this.itemHeight);
                this.ctx.stroke();

                // 文本内容
                this.ctx.fillStyle = '#1e293b';
                this.ctx.textBaseline = 'middle';

                const textY = y + this.itemHeight / 2;
                const leftPadding = this.padding;

                // 渲染序号
                this.ctx.fillStyle = '#64748b';
                this.ctx.fillText(`#${index + 1}`, leftPadding, textY);

                // 渲染主要内容
                this.ctx.fillStyle = '#1e293b';
                const mainText = typeof item === 'object' ?
                    (item.title || item.name || JSON.stringify(item)) :
                    String(item);
                this.ctx.fillText(mainText, leftPadding + 60, textY);

                // 渲染次要信息
                if (typeof item === 'object' && item.subtitle) {
                    this.ctx.fillStyle = '#64748b';
                    this.ctx.fillText(item.subtitle, leftPadding + 300, textY);
                }
            }

            updateStats() {
                document.getElementById('totalCount').textContent = this.data.length.toLocaleString();
                document.getElementById('visibleRange').textContent =
                    `${this.visibleStart}-${this.visibleEnd}`;
                document.getElementById('renderTime').textContent =
                    `${this.renderTime.toFixed(2)}ms`;

                // 估算内存使用
                const memoryUsage = (this.data.length * 100) / (1024 * 1024); // 粗略估算
                document.getElementById('memoryUsage').textContent =
                    `${memoryUsage.toFixed(2)}MB`;
            }

            onItemClick(index, item) {
                console.log('点击项目:', index, item);
                // 可以在这里添加自定义点击处理逻辑
            }

            // 公共API
            scrollToTop() {
                this.scrollTo(0);
            }

            scrollToBottom() {
                this.scrollTo(this.totalHeight);
            }

            scrollToIndex(index) {
                const targetScrollTop = index * this.itemHeight;
                this.scrollTo(targetScrollTop);
            }
        }

        // 初始化
        let virtualList;

        function initVirtualList() {
            const canvas = document.getElementById('listCanvas');
            virtualList = new CanvasVirtualList(canvas, {
                itemHeight: 50,
                padding: 15,
                fontSize: 14
            });

            // 生成初始数据
            generateData();
        }

        function generateData() {
            const count = parseInt(document.getElementById('dataCount').value);
            const data = [];

            for (let i = 0; i < count; i++) {
                data.push({
                    id: i,
                    title: `列表项 ${i + 1}`,
                    subtitle: `创建时间: ${new Date(Date.now() - Math.random() * 86400000 * 365).toLocaleDateString()}`,
                    value: Math.floor(Math.random() * 1000)
                });
            }

            virtualList.setData(data);
        }

        function scrollToTop() {
            virtualList.scrollToTop();
        }

        function scrollToBottom() {
            virtualList.scrollToBottom();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initVirtualList);
    </script>
</body>
</html>
