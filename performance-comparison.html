<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长列表渲染方案性能对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .method-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .method-header {
            padding: 1.5rem;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .method-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .method-description {
            font-size: 0.9rem;
            color: #64748b;
        }
        
        .method-content {
            height: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .list-container {
            height: 100%;
            overflow-y: auto;
        }
        
        .canvas-container {
            height: 100%;
            position: relative;
        }
        
        .method-stats {
            padding: 1rem 1.5rem;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            font-size: 0.8rem;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-label {
            color: #64748b;
            margin-bottom: 0.25rem;
        }
        
        .stat-value {
            font-weight: 600;
            font-size: 1.1em;
        }
        
        .controls {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .control-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .control-group:last-child {
            margin-bottom: 0;
        }
        
        button {
            padding: 0.5rem 1rem;
            border: 2px solid #3b82f6;
            border-radius: 6px;
            background: #3b82f6;
            color: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        button:hover {
            background: #2563eb;
            border-color: #2563eb;
        }
        
        input, select {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9rem;
        }
        
        .dom-item {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }
        
        .dom-item:hover {
            background-color: #f8fafc;
        }
        
        .dom-item:nth-child(even) {
            background-color: #f8fafc;
        }
        
        .item-content {
            flex: 1;
        }
        
        .item-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .item-subtitle {
            font-size: 0.8rem;
            color: #64748b;
        }
        
        .item-index {
            font-size: 0.8rem;
            color: #9ca3af;
            font-family: monospace;
        }
        
        .performance-warning {
            background: #fef3c7;
            color: #92400e;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem;
            font-size: 0.9rem;
        }
        
        .performance-good {
            background: #d1fae5;
            color: #065f46;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem;
            font-size: 0.9rem;
        }
        
        .virtual-list-item {
            position: absolute;
            left: 0;
            right: 0;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            background: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .virtual-list-item:nth-child(even) {
            background: #f8fafc;
        }
        
        .virtual-container {
            position: relative;
            height: 100%;
            overflow-y: auto;
        }
        
        .virtual-spacer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            pointer-events: none;
        }
        
        .virtual-viewport {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>长列表渲染方案性能对比</h1>
        <p>对比DOM渲染、虚拟列表、Canvas渲染等方案的性能表现</p>
    </div>
    
    <div class="container">
        <div class="controls">
            <div class="control-group">
                <label>测试数据量:</label>
                <select id="dataCount">
                    <option value="1000">1,000 条</option>
                    <option value="5000">5,000 条</option>
                    <option value="10000" selected>10,000 条</option>
                    <option value="50000">50,000 条</option>
                    <option value="100000">100,000 条</option>
                </select>
                <button onclick="runPerformanceTest()">开始性能测试</button>
                <button onclick="clearAllLists()">清空所有列表</button>
            </div>
            
            <div class="control-group">
                <label>操作测试:</label>
                <button onclick="scrollAllToTop()">全部回到顶部</button>
                <button onclick="scrollAllToBottom()">全部滚动到底部</button>
                <button onclick="scrollAllToMiddle()">全部滚动到中间</button>
            </div>
        </div>
        
        <div class="comparison-grid">
            <!-- DOM直接渲染 -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-title">DOM直接渲染</div>
                    <div class="method-description">传统方式，直接创建DOM元素</div>
                </div>
                <div class="method-content">
                    <div class="list-container" id="domList">
                        <div class="performance-warning">
                            ⚠️ 大数据量时可能导致页面卡顿
                        </div>
                    </div>
                </div>
                <div class="method-stats">
                    <div class="stat-item">
                        <div class="stat-label">渲染时间</div>
                        <div class="stat-value" id="domRenderTime">0ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">DOM节点</div>
                        <div class="stat-value" id="domNodeCount">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">内存使用</div>
                        <div class="stat-value" id="domMemory">0MB</div>
                    </div>
                </div>
            </div>
            
            <!-- 虚拟列表 -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-title">虚拟列表</div>
                    <div class="method-description">只渲染可视区域的DOM元素</div>
                </div>
                <div class="method-content">
                    <div class="virtual-container" id="virtualContainer">
                        <div class="virtual-spacer" id="virtualSpacer"></div>
                        <div class="virtual-viewport" id="virtualViewport"></div>
                        <div class="performance-good">
                            ✅ 高效渲染，内存占用低
                        </div>
                    </div>
                </div>
                <div class="method-stats">
                    <div class="stat-item">
                        <div class="stat-label">渲染时间</div>
                        <div class="stat-value" id="virtualRenderTime">0ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">可见节点</div>
                        <div class="stat-value" id="virtualNodeCount">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">内存使用</div>
                        <div class="stat-value" id="virtualMemory">0MB</div>
                    </div>
                </div>
            </div>
            
            <!-- Canvas渲染 -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-title">Canvas渲染</div>
                    <div class="method-description">使用Canvas绘制，无DOM操作</div>
                </div>
                <div class="method-content">
                    <div class="canvas-container">
                        <canvas id="canvasMethod"></canvas>
                        <div class="performance-good">
                            ✅ 极致性能，流畅滚动
                        </div>
                    </div>
                </div>
                <div class="method-stats">
                    <div class="stat-item">
                        <div class="stat-label">渲染时间</div>
                        <div class="stat-value" id="canvasRenderTime">0ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">FPS</div>
                        <div class="stat-value" id="canvasFPS">60</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">内存使用</div>
                        <div class="stat-value" id="canvasMemory">0MB</div>
                    </div>
                </div>
            </div>
            
            <!-- 时间分片渲染 -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-title">时间分片渲染</div>
                    <div class="method-description">使用requestAnimationFrame分批渲染</div>
                </div>
                <div class="method-content">
                    <div class="list-container" id="timeSlicingList">
                        <div class="performance-warning">
                            ⚠️ 渐进式渲染，可能出现闪烁
                        </div>
                    </div>
                </div>
                <div class="method-stats">
                    <div class="stat-item">
                        <div class="stat-label">渲染时间</div>
                        <div class="stat-value" id="timeSlicingRenderTime">0ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">分片数量</div>
                        <div class="stat-value" id="timeSlicingChunks">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">完成进度</div>
                        <div class="stat-value" id="timeSlicingProgress">0%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="advanced-canvas-list.js"></script>
    <script>
        let testData = [];
        let canvasList;
        let virtualListState = {
            data: [],
            scrollTop: 0,
            itemHeight: 60,
            containerHeight: 400,
            visibleStart: 0,
            visibleEnd: 0
        };
        
        // 生成测试数据
        function generateTestData(count) {
            const data = [];
            for (let i = 0; i < count; i++) {
                data.push({
                    id: i,
                    title: `测试项目 ${i + 1}`,
                    subtitle: `描述信息 ${Math.random().toString(36).substring(7)}`,
                    value: Math.floor(Math.random() * 1000)
                });
            }
            return data;
        }
        
        // DOM直接渲染
        function renderDOMList(data) {
            const startTime = performance.now();
            const container = document.getElementById('domList');
            
            // 清空容器
            container.innerHTML = '';
            
            // 如果数据量过大，显示警告
            if (data.length > 10000) {
                container.innerHTML = '<div class="performance-warning">⚠️ 数据量过大，可能导致浏览器卡顿，建议使用其他方案</div>';
                document.getElementById('domRenderTime').textContent = 'N/A';
                document.getElementById('domNodeCount').textContent = '0';
                document.getElementById('domMemory').textContent = '0MB';
                return;
            }
            
            // 创建DOM元素
            const fragment = document.createDocumentFragment();
            data.forEach((item, index) => {
                const div = document.createElement('div');
                div.className = 'dom-item';
                div.innerHTML = `
                    <div class="item-content">
                        <div class="item-title">${item.title}</div>
                        <div class="item-subtitle">${item.subtitle}</div>
                    </div>
                    <div class="item-index">#${index + 1}</div>
                `;
                fragment.appendChild(div);
            });
            
            container.appendChild(fragment);
            
            const renderTime = performance.now() - startTime;
            document.getElementById('domRenderTime').textContent = `${renderTime.toFixed(2)}ms`;
            document.getElementById('domNodeCount').textContent = data.length.toLocaleString();
            document.getElementById('domMemory').textContent = `${(data.length * 0.5 / 1024).toFixed(2)}MB`;
        }
        
        // 虚拟列表渲染
        function renderVirtualList(data) {
            const startTime = performance.now();
            virtualListState.data = data;
            
            const container = document.getElementById('virtualContainer');
            const spacer = document.getElementById('virtualSpacer');
            const viewport = document.getElementById('virtualViewport');
            
            // 计算总高度
            const totalHeight = data.length * virtualListState.itemHeight;
            spacer.style.height = totalHeight + 'px';
            
            // 绑定滚动事件
            container.onscroll = () => {
                virtualListState.scrollTop = container.scrollTop;
                updateVirtualList();
            };
            
            updateVirtualList();
            
            const renderTime = performance.now() - startTime;
            document.getElementById('virtualRenderTime').textContent = `${renderTime.toFixed(2)}ms`;
        }
        
        function updateVirtualList() {
            const { data, scrollTop, itemHeight, containerHeight } = virtualListState;
            const viewport = document.getElementById('virtualViewport');
            
            // 计算可见范围
            const start = Math.floor(scrollTop / itemHeight);
            const end = Math.min(start + Math.ceil(containerHeight / itemHeight) + 1, data.length);
            
            virtualListState.visibleStart = start;
            virtualListState.visibleEnd = end;
            
            // 清空视口
            viewport.innerHTML = '';
            
            // 渲染可见项
            for (let i = start; i < end; i++) {
                const item = data[i];
                const div = document.createElement('div');
                div.className = 'virtual-list-item';
                div.style.top = (i * itemHeight) + 'px';
                div.style.height = itemHeight + 'px';
                div.innerHTML = `
                    <div class="item-content">
                        <div class="item-title">${item.title}</div>
                        <div class="item-subtitle">${item.subtitle}</div>
                    </div>
                    <div class="item-index">#${i + 1}</div>
                `;
                viewport.appendChild(div);
            }
            
            document.getElementById('virtualNodeCount').textContent = (end - start).toString();
            document.getElementById('virtualMemory').textContent = `${((end - start) * 0.5 / 1024).toFixed(2)}MB`;
        }
        
        // Canvas渲染
        function renderCanvasList(data) {
            const canvas = document.getElementById('canvasMethod');
            
            if (canvasList) {
                canvasList.destroy();
            }
            
            canvasList = new AdvancedCanvasVirtualList(canvas, {
                itemHeight: 60,
                padding: 16,
                fontSize: 14
            });
            
            canvasList.setData(data);
            
            // 更新统计信息
            const updateCanvasStats = () => {
                const stats = canvasList.getPerformanceStats();
                document.getElementById('canvasRenderTime').textContent = `${stats.renderTime.toFixed(2)}ms`;
                document.getElementById('canvasFPS').textContent = `${stats.fps}`;
                document.getElementById('canvasMemory').textContent = `${stats.memoryUsage.toFixed(2)}MB`;
            };
            
            updateCanvasStats();
            setInterval(updateCanvasStats, 1000);
        }
        
        // 时间分片渲染
        function renderTimeSlicingList(data) {
            const container = document.getElementById('timeSlicingList');
            container.innerHTML = '';
            
            let currentIndex = 0;
            const chunkSize = 50;
            const totalChunks = Math.ceil(data.length / chunkSize);
            let currentChunk = 0;
            
            const startTime = performance.now();
            
            function renderChunk() {
                const fragment = document.createDocumentFragment();
                const endIndex = Math.min(currentIndex + chunkSize, data.length);
                
                for (let i = currentIndex; i < endIndex; i++) {
                    const item = data[i];
                    const div = document.createElement('div');
                    div.className = 'dom-item';
                    div.innerHTML = `
                        <div class="item-content">
                            <div class="item-title">${item.title}</div>
                            <div class="item-subtitle">${item.subtitle}</div>
                        </div>
                        <div class="item-index">#${i + 1}</div>
                    `;
                    fragment.appendChild(div);
                }
                
                container.appendChild(fragment);
                currentIndex = endIndex;
                currentChunk++;
                
                // 更新进度
                const progress = (currentChunk / totalChunks * 100).toFixed(1);
                document.getElementById('timeSlicingProgress').textContent = `${progress}%`;
                document.getElementById('timeSlicingChunks').textContent = currentChunk.toString();
                
                if (currentIndex < data.length) {
                    requestAnimationFrame(renderChunk);
                } else {
                    const renderTime = performance.now() - startTime;
                    document.getElementById('timeSlicingRenderTime').textContent = `${renderTime.toFixed(2)}ms`;
                }
            }
            
            renderChunk();
        }
        
        // 运行性能测试
        function runPerformanceTest() {
            const count = parseInt(document.getElementById('dataCount').value);
            testData = generateTestData(count);
            
            console.log(`开始性能测试，数据量: ${count}`);
            
            // 依次测试各种方案
            setTimeout(() => renderDOMList(testData), 100);
            setTimeout(() => renderVirtualList(testData), 200);
            setTimeout(() => renderCanvasList(testData), 300);
            setTimeout(() => renderTimeSlicingList(testData), 400);
        }
        
        // 清空所有列表
        function clearAllLists() {
            document.getElementById('domList').innerHTML = '<div class="performance-warning">⚠️ 大数据量时可能导致页面卡顿</div>';
            document.getElementById('virtualContainer').querySelector('.virtual-viewport').innerHTML = '';
            document.getElementById('timeSlicingList').innerHTML = '<div class="performance-warning">⚠️ 渐进式渲染，可能出现闪烁</div>';
            
            if (canvasList) {
                canvasList.setData([]);
            }
            
            // 重置统计信息
            ['domRenderTime', 'virtualRenderTime', 'canvasRenderTime', 'timeSlicingRenderTime'].forEach(id => {
                document.getElementById(id).textContent = '0ms';
            });
            
            ['domNodeCount', 'virtualNodeCount', 'timeSlicingChunks'].forEach(id => {
                document.getElementById(id).textContent = '0';
            });
            
            ['domMemory', 'virtualMemory', 'canvasMemory'].forEach(id => {
                document.getElementById(id).textContent = '0MB';
            });
            
            document.getElementById('canvasFPS').textContent = '60';
            document.getElementById('timeSlicingProgress').textContent = '0%';
        }
        
        // 滚动操作
        function scrollAllToTop() {
            document.getElementById('domList').scrollTop = 0;
            document.getElementById('virtualContainer').scrollTop = 0;
            if (canvasList) canvasList.scrollTo(0);
            document.getElementById('timeSlicingList').scrollTop = 0;
        }
        
        function scrollAllToBottom() {
            const containers = ['domList', 'virtualContainer', 'timeSlicingList'];
            containers.forEach(id => {
                const el = document.getElementById(id);
                el.scrollTop = el.scrollHeight;
            });
            if (canvasList) canvasList.scrollTo(canvasList.state.totalHeight);
        }
        
        function scrollAllToMiddle() {
            const containers = ['domList', 'virtualContainer', 'timeSlicingList'];
            containers.forEach(id => {
                const el = document.getElementById(id);
                el.scrollTop = el.scrollHeight / 2;
            });
            if (canvasList) canvasList.scrollTo(canvasList.state.totalHeight / 2);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化Canvas
            const canvas = document.getElementById('canvasMethod');
            const rect = canvas.parentElement.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
            
            // 运行初始测试
            runPerformanceTest();
        });
    </script>
</body>
</html>
