/**
 * 高级Canvas虚拟列表组件
 * 支持动态高度、多列布局、选择、搜索等功能
 */
class AdvancedCanvasVirtualList {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.container = canvas.parentElement;
        
        // 配置参数
        this.config = {
            itemHeight: options.itemHeight || 60,
            padding: options.padding || 16,
            fontSize: options.fontSize || 14,
            lineHeight: options.lineHeight || 20,
            columns: options.columns || 1,
            gap: options.gap || 10,
            bufferSize: options.bufferSize || 10,
            enableSelection: options.enableSelection !== false,
            enableHover: options.enableHover !== false,
            enableSearch: options.enableSearch !== false,
            ...options
        };
        
        // 状态管理
        this.state = {
            data: [],
            filteredData: [],
            scrollTop: 0,
            containerHeight: 0,
            containerWidth: 0,
            totalHeight: 0,
            visibleStart: 0,
            visibleEnd: 0,
            selectedIndices: new Set(),
            hoveredIndex: -1,
            searchQuery: '',
            isMultiSelect: false
        };
        
        // 性能监控
        this.performance = {
            renderTime: 0,
            lastRenderTime: 0,
            frameCount: 0,
            fps: 0
        };
        
        // 事件回调
        this.callbacks = {
            onItemClick: options.onItemClick || (() => {}),
            onItemDoubleClick: options.onItemDoubleClick || (() => {}),
            onSelectionChange: options.onSelectionChange || (() => {}),
            onScroll: options.onScroll || (() => {})
        };
        
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.bindEvents();
        this.startPerformanceMonitor();
    }
    
    setupCanvas() {
        const rect = this.container.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        
        this.state.containerHeight = rect.height;
        this.state.containerWidth = rect.width;
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        this.ctx.scale(dpr, dpr);
        this.ctx.font = `${this.config.fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
        this.ctx.textBaseline = 'top';
    }
    
    bindEvents() {
        // 滚轮事件
        this.container.addEventListener('wheel', (e) => {
            e.preventDefault();
            this.handleScroll(e.deltaY);
        }, { passive: false });
        
        // 鼠标事件
        this.canvas.addEventListener('mousemove', (e) => {
            if (this.config.enableHover) {
                this.handleMouseMove(e);
            }
        });
        
        this.canvas.addEventListener('click', (e) => {
            this.handleClick(e);
        });
        
        this.canvas.addEventListener('dblclick', (e) => {
            this.handleDoubleClick(e);
        });
        
        // 键盘事件
        this.canvas.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });
        
        // 使canvas可聚焦
        this.canvas.tabIndex = 0;
        
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.setupCanvas();
            this.calculateLayout();
            this.render();
        });
    }
    
    handleScroll(deltaY) {
        const maxScroll = Math.max(0, this.state.totalHeight - this.state.containerHeight);
        const newScrollTop = Math.max(0, Math.min(
            this.state.scrollTop + deltaY,
            maxScroll
        ));
        
        if (newScrollTop !== this.state.scrollTop) {
            this.state.scrollTop = newScrollTop;
            this.calculateVisibleRange();
            this.render();
            this.callbacks.onScroll(this.state.scrollTop, maxScroll);
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const y = e.clientY - rect.top;
        const index = this.getIndexAtPosition(y);
        
        if (index !== this.state.hoveredIndex) {
            this.state.hoveredIndex = index;
            this.render();
        }
    }
    
    handleClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const y = e.clientY - rect.top;
        const index = this.getIndexAtPosition(y);
        
        if (index >= 0 && index < this.state.filteredData.length) {
            if (this.config.enableSelection) {
                this.handleSelection(index, e.ctrlKey || e.metaKey, e.shiftKey);
            }
            
            this.callbacks.onItemClick(index, this.state.filteredData[index], e);
        }
    }
    
    handleDoubleClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const y = e.clientY - rect.top;
        const index = this.getIndexAtPosition(y);
        
        if (index >= 0 && index < this.state.filteredData.length) {
            this.callbacks.onItemDoubleClick(index, this.state.filteredData[index], e);
        }
    }
    
    handleSelection(index, isCtrlKey, isShiftKey) {
        const prevSelection = new Set(this.state.selectedIndices);
        
        if (isShiftKey && this.state.selectedIndices.size > 0) {
            // 范围选择
            const lastSelected = Math.max(...this.state.selectedIndices);
            const start = Math.min(index, lastSelected);
            const end = Math.max(index, lastSelected);
            
            if (!isCtrlKey) {
                this.state.selectedIndices.clear();
            }
            
            for (let i = start; i <= end; i++) {
                this.state.selectedIndices.add(i);
            }
        } else if (isCtrlKey) {
            // 多选
            if (this.state.selectedIndices.has(index)) {
                this.state.selectedIndices.delete(index);
            } else {
                this.state.selectedIndices.add(index);
            }
        } else {
            // 单选
            this.state.selectedIndices.clear();
            this.state.selectedIndices.add(index);
        }
        
        // 检查选择是否发生变化
        if (!this.setsEqual(prevSelection, this.state.selectedIndices)) {
            this.callbacks.onSelectionChange(Array.from(this.state.selectedIndices));
            this.render();
        }
    }
    
    handleKeyDown(e) {
        switch(e.key) {
            case 'ArrowUp':
                e.preventDefault();
                this.navigateSelection(-1, e.shiftKey);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.navigateSelection(1, e.shiftKey);
                break;
            case 'PageUp':
                e.preventDefault();
                this.handleScroll(-this.state.containerHeight);
                break;
            case 'PageDown':
                e.preventDefault();
                this.handleScroll(this.state.containerHeight);
                break;
            case 'Home':
                e.preventDefault();
                this.scrollTo(0);
                break;
            case 'End':
                e.preventDefault();
                this.scrollTo(this.state.totalHeight);
                break;
            case 'a':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.selectAll();
                }
                break;
            case 'Escape':
                this.clearSelection();
                break;
        }
    }
    
    navigateSelection(direction, isShiftKey) {
        if (this.state.selectedIndices.size === 0) {
            this.handleSelection(0, false, false);
            return;
        }
        
        const currentIndex = Math.max(...this.state.selectedIndices);
        const newIndex = Math.max(0, Math.min(
            currentIndex + direction,
            this.state.filteredData.length - 1
        ));
        
        if (newIndex !== currentIndex) {
            this.handleSelection(newIndex, false, isShiftKey);
            this.scrollToIndex(newIndex);
        }
    }
    
    getIndexAtPosition(y) {
        const adjustedY = y + this.state.scrollTop;
        return Math.floor(adjustedY / this.config.itemHeight);
    }
    
    calculateLayout() {
        this.state.totalHeight = this.state.filteredData.length * this.config.itemHeight;
        this.calculateVisibleRange();
    }
    
    calculateVisibleRange() {
        const start = Math.floor(this.state.scrollTop / this.config.itemHeight);
        const visibleCount = Math.ceil(this.state.containerHeight / this.config.itemHeight);
        
        this.state.visibleStart = Math.max(0, start - this.config.bufferSize);
        this.state.visibleEnd = Math.min(
            this.state.filteredData.length - 1,
            start + visibleCount + this.config.bufferSize
        );
    }
    
    startPerformanceMonitor() {
        setInterval(() => {
            this.performance.fps = this.performance.frameCount;
            this.performance.frameCount = 0;
        }, 1000);
    }
    
    setsEqual(set1, set2) {
        if (set1.size !== set2.size) return false;
        for (let item of set1) {
            if (!set2.has(item)) return false;
        }
        return true;
    }
    
    // 公共API方法
    setData(data) {
        this.state.data = data;
        this.applyFilter();
    }
    
    applyFilter() {
        if (!this.config.enableSearch || !this.state.searchQuery) {
            this.state.filteredData = this.state.data;
        } else {
            const query = this.state.searchQuery.toLowerCase();
            this.state.filteredData = this.state.data.filter(item => {
                const searchText = typeof item === 'object' ? 
                    JSON.stringify(item).toLowerCase() : 
                    String(item).toLowerCase();
                return searchText.includes(query);
            });
        }
        
        this.calculateLayout();
        this.render();
    }
    
    search(query) {
        this.state.searchQuery = query;
        this.applyFilter();
    }
    
    scrollTo(scrollTop) {
        const maxScroll = Math.max(0, this.state.totalHeight - this.state.containerHeight);
        this.state.scrollTop = Math.max(0, Math.min(scrollTop, maxScroll));
        this.calculateVisibleRange();
        this.render();
    }
    
    scrollToIndex(index) {
        const targetScrollTop = index * this.config.itemHeight;
        const maxVisibleScrollTop = targetScrollTop - this.state.containerHeight + this.config.itemHeight;
        
        if (this.state.scrollTop > targetScrollTop) {
            this.scrollTo(targetScrollTop);
        } else if (this.state.scrollTop < maxVisibleScrollTop) {
            this.scrollTo(maxVisibleScrollTop);
        }
    }
    
    selectAll() {
        if (this.config.enableSelection) {
            this.state.selectedIndices.clear();
            for (let i = 0; i < this.state.filteredData.length; i++) {
                this.state.selectedIndices.add(i);
            }
            this.callbacks.onSelectionChange(Array.from(this.state.selectedIndices));
            this.render();
        }
    }
    
    clearSelection() {
        if (this.state.selectedIndices.size > 0) {
            this.state.selectedIndices.clear();
            this.callbacks.onSelectionChange([]);
            this.render();
        }
    }
    
    getSelectedItems() {
        return Array.from(this.state.selectedIndices).map(index =>
            this.state.filteredData[index]
        );
    }

    render() {
        const startTime = performance.now();

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 渲染背景
        this.renderBackground();

        // 渲染可见项
        for (let i = this.state.visibleStart; i <= this.state.visibleEnd; i++) {
            if (i >= this.state.filteredData.length) break;

            const item = this.state.filteredData[i];
            const y = i * this.config.itemHeight - this.state.scrollTop;

            // 跳过不在可视区域的项
            if (y + this.config.itemHeight < 0 || y > this.state.containerHeight) continue;

            this.renderItem(item, i, y);
        }

        // 渲染滚动指示器
        this.renderScrollIndicator();

        // 更新性能统计
        this.performance.renderTime = performance.now() - startTime;
        this.performance.frameCount++;
    }

    renderBackground() {
        // 渲染主背景
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(0, 0, this.state.containerWidth, this.state.containerHeight);
    }

    renderItem(item, index, y) {
        const isSelected = this.state.selectedIndices.has(index);
        const isHovered = this.state.hoveredIndex === index;
        const isEven = index % 2 === 0;

        // 计算项目区域
        const itemRect = {
            x: 0,
            y: y,
            width: this.state.containerWidth,
            height: this.config.itemHeight
        };

        // 渲染背景
        this.renderItemBackground(itemRect, isSelected, isHovered, isEven);

        // 渲染内容
        this.renderItemContent(item, index, itemRect);

        // 渲染边框
        this.renderItemBorder(itemRect, isSelected);
    }

    renderItemBackground(rect, isSelected, isHovered, isEven) {
        let backgroundColor;

        if (isSelected) {
            backgroundColor = '#3b82f6';
        } else if (isHovered) {
            backgroundColor = '#e0f2fe';
        } else {
            backgroundColor = isEven ? '#ffffff' : '#f8fafc';
        }

        this.ctx.fillStyle = backgroundColor;
        this.ctx.fillRect(rect.x, rect.y, rect.width, rect.height);
    }

    renderItemContent(item, index, rect) {
        const isSelected = this.state.selectedIndices.has(index);
        const textColor = isSelected ? '#ffffff' : '#1e293b';
        const secondaryTextColor = isSelected ? '#e2e8f0' : '#64748b';

        this.ctx.fillStyle = textColor;

        const padding = this.config.padding;
        const contentY = rect.y + padding;

        // 渲染序号
        this.ctx.font = `${this.config.fontSize - 2}px monospace`;
        this.ctx.fillStyle = secondaryTextColor;
        this.ctx.fillText(`#${index + 1}`, padding, contentY);

        // 渲染主要内容
        this.ctx.font = `${this.config.fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
        this.ctx.fillStyle = textColor;

        let mainText = '';
        let subtitle = '';

        if (typeof item === 'object') {
            mainText = item.title || item.name || item.label || `Item ${index + 1}`;
            subtitle = item.subtitle || item.description || item.value || '';
        } else {
            mainText = String(item);
        }

        // 主标题
        const titleY = contentY + 2;
        this.ctx.fillText(this.truncateText(mainText, rect.width - padding * 2 - 60), padding + 60, titleY);

        // 副标题
        if (subtitle) {
            this.ctx.font = `${this.config.fontSize - 2}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
            this.ctx.fillStyle = secondaryTextColor;
            const subtitleY = titleY + this.config.lineHeight;
            this.ctx.fillText(this.truncateText(String(subtitle), rect.width - padding * 2 - 60), padding + 60, subtitleY);
        }

        // 渲染右侧信息
        if (typeof item === 'object' && item.rightText) {
            this.ctx.font = `${this.config.fontSize - 1}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
            this.ctx.fillStyle = secondaryTextColor;
            this.ctx.textAlign = 'right';
            this.ctx.fillText(String(item.rightText), rect.width - padding, contentY + 2);
            this.ctx.textAlign = 'left';
        }
    }

    renderItemBorder(rect, isSelected) {
        // 底部分割线
        this.ctx.strokeStyle = isSelected ? 'rgba(255,255,255,0.2)' : '#e2e8f0';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(rect.x, rect.y + rect.height);
        this.ctx.lineTo(rect.x + rect.width, rect.y + rect.height);
        this.ctx.stroke();
    }

    renderScrollIndicator() {
        if (this.state.totalHeight <= this.state.containerHeight) return;

        const scrollbarWidth = 8;
        const scrollbarHeight = this.state.containerHeight;
        const thumbHeight = Math.max(20,
            (this.state.containerHeight / this.state.totalHeight) * scrollbarHeight
        );
        const maxScroll = this.state.totalHeight - this.state.containerHeight;
        const thumbTop = (this.state.scrollTop / maxScroll) * (scrollbarHeight - thumbHeight);

        // 滚动条背景
        this.ctx.fillStyle = 'rgba(0,0,0,0.1)';
        this.ctx.fillRect(
            this.state.containerWidth - scrollbarWidth,
            0,
            scrollbarWidth,
            scrollbarHeight
        );

        // 滚动条滑块
        this.ctx.fillStyle = 'rgba(0,0,0,0.3)';
        this.ctx.fillRect(
            this.state.containerWidth - scrollbarWidth,
            thumbTop,
            scrollbarWidth,
            thumbHeight
        );
    }

    truncateText(text, maxWidth) {
        const ellipsis = '...';
        let truncated = text;

        while (this.ctx.measureText(truncated).width > maxWidth && truncated.length > 0) {
            truncated = truncated.slice(0, -1);
        }

        if (truncated.length < text.length) {
            truncated = truncated.slice(0, -ellipsis.length) + ellipsis;
        }

        return truncated;
    }

    // 性能优化方法
    requestRender() {
        if (!this.renderRequested) {
            this.renderRequested = true;
            requestAnimationFrame(() => {
                this.render();
                this.renderRequested = false;
            });
        }
    }

    // 获取性能统计
    getPerformanceStats() {
        return {
            renderTime: this.performance.renderTime,
            fps: this.performance.fps,
            visibleItems: this.state.visibleEnd - this.state.visibleStart + 1,
            totalItems: this.state.filteredData.length,
            memoryUsage: this.estimateMemoryUsage()
        };
    }

    estimateMemoryUsage() {
        // 粗略估算内存使用量（字节）
        const itemSize = 100; // 每个数据项大约100字节
        const canvasSize = this.canvas.width * this.canvas.height * 4; // RGBA
        return (this.state.data.length * itemSize + canvasSize) / (1024 * 1024); // MB
    }

    // 销毁方法
    destroy() {
        // 清理事件监听器
        this.container.removeEventListener('wheel', this.handleScroll);
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        this.canvas.removeEventListener('click', this.handleClick);
        this.canvas.removeEventListener('dblclick', this.handleDoubleClick);
        this.canvas.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('resize', this.setupCanvas);

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
}
